# 记账设置功能使用说明

## 🎯 功能概述

新增的记账设置功能允许用户自定义默认的记账行为，包括记账方式、默认卡片选择等，提升记账效率和用户体验。

## 📱 如何访问

1. **从主页进入**：点击主页右下角的设置按钮（齿轮图标）
2. **在设置页面**：找到"记账设置"分组，点击"记账偏好"
3. **弹出设置页面**：会以Sheet形式弹出记账设置页面

## ⚙️ 功能详解

### 1. 默认记账方式

**功能说明**：选择你偏好的记账方式

**选项**：
- **AI智能记账**：使用AI自动识别交易信息
- **手动记账**：手动输入交易详情

**使用场景**：
- 选择AI记账：适合快速记账，让AI帮你识别分类和金额
- 选择手动记账：适合精确控制，手动选择每个细节

### 2. 默认卡片设置

**功能说明**：为不同类型的交易设置默认卡片

**设置项**：
- **默认支出卡片**：用于支出交易的默认卡片
- **默认收入卡片**：用于收入交易的默认卡片

**使用方法**：
1. 点击对应的下拉菜单
2. 选择"不设置默认卡片"或选择具体卡片
3. 设置后，创建对应类型交易时会自动选中该卡片

### 3. 智能选择

**功能说明**：让应用记住你的使用习惯

**选项**：
- **使用上次选择的卡片**：优先使用上次交易时选择的卡片

**工作原理**：
- 开启后：应用会记住你上次支出/收入时使用的卡片
- 下次创建同类型交易时，优先选择上次使用的卡片
- 如果上次使用的卡片不可用，则回退到默认卡片

### 4. 重置设置

**功能说明**：恢复到默认设置

**操作**：点击"重置所有设置"按钮

**效果**：
- 记账方式重置为"手动记账"
- 清除所有默认卡片设置
- 关闭"使用上次选择的卡片"功能
- 清除所有历史使用记录

## 🔄 卡片选择优先级

当创建新交易时，系统按以下优先级选择卡片：

1. **最高优先级**：如果开启"使用上次选择的卡片"，且上次使用的卡片可用
2. **中等优先级**：如果设置了默认卡片，且该卡片可用
3. **最低优先级**：选择第一张可用的储蓄卡

## 💾 数据存储

所有设置使用UserDefaults存储，包括：
- `defaultRecordingMode`：默认记账方式
- `defaultExpenseCardId`：默认支出卡片ID
- `defaultIncomeCardId`：默认收入卡片ID
- `useLastUsedCards`：是否使用上次选择的卡片
- `lastUsedExpenseCardId`：上次使用的支出卡片ID
- `lastUsedIncomeCardId`：上次使用的收入卡片ID

## 🔧 技术实现

### 核心组件

1. **TransactionDefaultsManager**：单例管理器，处理所有默认设置
2. **TransactionSettingsView**：设置页面UI
3. **TransactionSettingsVM**：设置页面视图模型

### 集成方式

在创建交易时调用：
```swift
// 获取推荐的支出卡片
let recommendedCardId = TransactionDefaultsManager.shared.getRecommendedExpenseCardId(
  from: availableCards
)

// 更新上次使用的卡片
TransactionDefaultsManager.shared.updateLastUsedCard(
  cardId: selectedCardId, 
  for: .expense
)
```

## 🎨 设计特点

- **直观的界面**：使用卡片式设计，清晰的视觉层次
- **即时反馈**：设置更改立即生效，有触觉反馈
- **智能提示**：每个设置都有详细的说明文字
- **安全操作**：重置功能有明确的警告样式

## 🚀 未来扩展

可以考虑添加的功能：
- 按时间段设置不同的默认卡片
- 按金额范围自动选择卡片
- 基于地理位置的智能卡片推荐
- 更多的记账方式选项

---

这个功能大大提升了记账的便利性，让用户可以根据自己的习惯定制记账体验！
