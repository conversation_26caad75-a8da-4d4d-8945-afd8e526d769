import Combine
import SwiftUI

/// 卡包视图模型
///
/// 从DataManagement获取卡片数据，计算净资产统计，支持卡片类型筛选。
final class CardBagVM: ObservableObject {

  // MARK: - 数据源

  /// 数据管理器
  let dataManager: DataManagement

  /// 卡片点击回调
  private var onCardTap: ((CardModel) -> Void)?

  // MARK: - UI数据

  /// 净资产金额（本位货币）
  @Published var netAssetAmount: Double = 0

  /// 本位货币符号
  @Published var netAssetSymbol: String = "¥"

  /// 当前筛选类型
  @Published var selectedCardType: CardFilterType = .all {
    didSet { updateFilteredCards() }
  }

  /// 筛选后的卡片数据（符合CardVM格式）
  @Published var filteredCardVMs: [CardVM] = []

  /// 活跃卡片数量
  @Published var activeCardCount: Int = 0

  /// 卡片类型文本（用于显示统计信息）
  @Published var cardTypeText: String = "卡片"

  /// 筛选显示文本（用于筛选按钮）
  var filterDisplayText: String {
    switch selectedCardType {
    case .all:
      return "所有卡片"
    case .debit:
      return "储蓄卡"
    case .credit:
      return "信用卡"
    }
  }

  // MARK: - 初始化

  init(dataManager: DataManagement, onCardTap: ((CardModel) -> Void)? = nil) {
    self.dataManager = dataManager
    self.onCardTap = onCardTap

    // 处理初始数据
    processData()
  }

  // MARK: - 公共方法

  /// 更新筛选类型
  func updateCardType(_ type: CardFilterType) {
    if selectedCardType != type {
      selectedCardType = type
      // updateFilteredCards() 会在didSet中自动调用
    }
  }

  /// 设置卡片点击回调
  func setCardTapHandler(_ handler: @escaping (CardModel) -> Void) {
    onCardTap = handler
    // 重新创建CardVM以应用新的回调
    updateFilteredCards()
  }

  // MARK: - 核心数据处理

  /// 处理数据的核心方法
  /// 1. 计算净资产统计（参考HomeVM）
  /// 2. 筛选卡片数据
  /// 3. 转换为CardVM格式
  private func processData() {
    // 1. 计算资产统计（与HomeVM完全一致）
    calculateAssetStatistics()

    // 2. 更新筛选后的卡片
    updateFilteredCards()
  }

  /// 计算资产统计（参考HomeVM的实现）
  private func calculateAssetStatistics() {
    // 获取用户设置的本位货币信息
    let baseCurrencyCode = CurrencyService.shared.baseCurrencyCode
    netAssetSymbol = dataManager.currencies.first { $0.code == baseCurrencyCode }?.symbol ?? "¥"

    // 使用CurrencyService的标准计算方法
    let result = CurrencyService.shared.calculateCardsDetailed(
      cards: dataManager.cards,
      currencies: dataManager.currencies,
      baseCurrencyCode: baseCurrencyCode,
      filterCreditCard: nil,  // 计算所有类型的卡片
      debug: false
    )

    // 更新Published属性
    netAssetAmount = result.netCards
  }

  /// 更新筛选后的卡片数据
  private func updateFilteredCards() {
    // 1. 根据筛选类型过滤卡片
    let filteredCards = dataManager.cards.filter { card in
      switch selectedCardType {
      case .all:
        return true
      case .credit:
        return card.isCredit
      case .debit:
        return !card.isCredit
      }
    }

    // 2. 按order排序
    let sortedCards = filteredCards.sorted { $0.order < $1.order }

    // 3. 转换为CardVM格式（不设置onTap，由外部处理）
    filteredCardVMs = sortedCards.map { card in
      CardVM.fromCard(card, onTap: nil)
    }

    // 4. 更新统计信息
    activeCardCount = filteredCards.count
    cardTypeText = selectedCardType.rawValue
  }

  // MARK: - ActionSheet ViewModel Factory

  /// 创建卡片筛选表单ViewModel
  /// - Parameter showFilterSheet: 控制筛选表单显示的绑定
  /// - Returns: 配置好的CardFilterSheetVM实例
  func createCardFilterSheetVM(showFilterSheet: Binding<Bool>) -> CardFilterSheetVM {
    return CardFilterSheetVM(
      dataManager: dataManager,
      selectedFilter: Binding(
        get: {
          switch self.selectedCardType {
          case .all: return nil
          case .debit: return false
          case .credit: return true
          }
        },
        set: { newValue in
          let newType: CardFilterType
          switch newValue {
          case nil: newType = .all
          case false: newType = .debit
          case true: newType = .credit
          }
          withAnimation(.easeInOut(duration: 0.4)) {
            self.updateCardType(newType)
          }
        }
      ),
      dismiss: { showFilterSheet.wrappedValue = false },
      onFilterChanged: { [weak self] newFilter in
        let newType: CardFilterType
        switch newFilter {
        case nil: newType = .all
        case false: newType = .debit
        case true: newType = .credit
        }
        withAnimation(.easeInOut(duration: 0.4)) {
          self?.updateCardType(newType)
        }
        showFilterSheet.wrappedValue = false
      }
    )
  }
}

// MARK: - 卡片筛选类型

/// 卡片筛选类型
enum CardFilterType: String, CaseIterable {
  case all = "卡片"
  case credit = "信用卡"
  case debit = "储蓄卡"
}
