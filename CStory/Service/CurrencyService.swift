import Foundation
import SwiftUI

/// 货币服务
///
/// 提供货币转换、格式化、本位币设置等功能，使用单例模式确保全局唯一实例。
final class CurrencyService {
  /// 共享实例
  static let shared = CurrencyService()

  /// 私有构造方法，确保单例模式
  private init() {
  }

  /// 获取本位币代码
  /// - Returns: 本位币代码，默认为"CNY"
  var baseCurrencyCode: String {
    UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
  }

  /// 设置本位币代码
  /// - Parameter code: 新的本位币代码
  func setBaseCurrencyCode(_ code: String) {
    UserDefaults.standard.set(code, forKey: "baseCurrencyCode")
  }

  /// 获取货币名称和符号映射
  /// - Returns: 包含货币名称映射和符号映射的元组
  static func getCurrencyMappings() -> ([String: String], [String: String]) {
    guard let url = Bundle.main.url(forResource: "CurrencyMappings", withExtension: "json"),
      let data = try? Data(contentsOf: url),
      let mappings = try? JSONDecoder().decode(CurrencyMappingsResponse.self, from: data)
    else {
      print("❌ 无法加载货币映射数据，使用默认值")
      return (["CNY": "人民币"], ["CNY": "¥"])
    }

    return (mappings.currencyNames, mappings.currencySymbols)
  }

  /// 获取常用货币代码列表
  /// - Returns: 常用货币代码数组
  static func getPriorityCurrencies() -> [String] {
    guard let url = Bundle.main.url(forResource: "CurrencyMappings", withExtension: "json"),
      let data = try? Data(contentsOf: url),
      let mappings = try? JSONDecoder().decode(CurrencyMappingsResponse.self, from: data)
    else {
      print("❌ 无法加载常用货币数据，使用默认值")
      return ["CNY", "USD", "EUR", "GBP", "JPY"]
    }

    return mappings.priorityCurrencies
  }

  /// 获取货币符号
  /// - Parameters:
  ///   - code: 货币代码
  ///   - currencies: 可用货币列表
  /// - Returns: 货币符号，默认为"¥"
  func getCurrencySymbol(for code: String, from currencies: [CurrencyModel]) -> String {
    currencies.first { $0.code == code }?.symbol ?? "¥"
  }

  /// 获取货币汇率
  /// - Parameters:
  ///   - sourceCurrency: 源货币代码
  ///   - targetCurrency: 目标货币代码
  ///   - currencies: 可用货币列表
  /// - Returns: 转换汇率值
  func getCurrencyRate(
    from sourceCurrency: String,
    to targetCurrency: String,
    currencies: [CurrencyModel]
  ) -> Double {
    if sourceCurrency == targetCurrency {
      return 1.0
    }

    // 从源货币转换到CNY
    let sourceRate = currencies.first { $0.code == sourceCurrency }?.rate ?? 1.0

    // 从CNY转换到目标货币
    let targetRate = currencies.first { $0.code == targetCurrency }?.rate ?? 1.0

    // 计算最终汇率
    return sourceRate / targetRate
  }

  /// 格式化金额
  /// - Parameters:
  ///   - amount: 金额数值
  ///   - currencyCode: 货币代码
  ///   - currencies: 可用货币列表
  /// - Returns: 格式化后的金额字符串
  func formatAmount(
    _ amount: Double,
    currencyCode: String,
    currencies: [CurrencyModel]
  ) -> String {
    let symbol = getCurrencySymbol(for: currencyCode, from: currencies)
    let formattedNumber = NumberFormatService.shared.formatAmount(abs(amount))

    if amount < 0 {
      return "-\(symbol)\(formattedNumber)"
    } else {
      return "\(symbol)\(formattedNumber)"
    }
  }

  /// 转换货币金额
  /// - Parameters:
  ///   - amount: 原始金额
  ///   - fromCurrency: 原始货币代码
  ///   - toCurrency: 目标货币代码
  ///   - currencies: 可用货币列表
  /// - Returns: 转换后的金额
  func convertAmount(
    _ amount: Double,
    from fromCurrency: String,
    to toCurrency: String,
    currencies: [CurrencyModel]
  ) -> Double {
    let rate = getCurrencyRate(from: fromCurrency, to: toCurrency, currencies: currencies)
    return amount * rate
  }

  /// 资产计算结果结构体
  struct CardCalculationResult {
    /// 总资产
    let totalCards: Double
    /// 总负债
    let totalLiabilities: Double
    /// 净资产
    let netCards: Double
    /// 活跃卡片数量
    let activeCardCount: Int
  }

  /// 计算卡片资产、负债和净资产
  /// - Parameters:
  ///   - cards: 卡片列表
  ///   - currencies: 货币列表
  ///   - baseCurrencyCode: 基准货币代码
  ///   - filterCreditCard: 卡片类型过滤器
  ///   - debug: 是否打印调试信息
  /// - Returns: 计算结果结构体
  /// - Note: 只统计 isSelected 和 isStatistics 都为 true 的卡片
  func calculateCardsDetailed(
    cards: [CardModel],
    currencies: [CurrencyModel],
    baseCurrencyCode: String? = nil,
    filterCreditCard: Bool? = nil,
    debug: Bool = false
  ) -> CardCalculationResult {
    let baseCurrency = baseCurrencyCode ?? self.baseCurrencyCode
    var totalCards: Double = 0
    var totalLiabilities: Double = 0
    var activeCardCount: Int = 0

    if debug {
      print("===== 净资产计算开始 =====")
      print("基准货币: \(baseCurrency)")
      if let filterCreditCard = filterCreditCard {
        print("过滤器: \(filterCreditCard ? "仅信用卡" : "仅储蓄卡")")
      } else {
        print("过滤器: 全部卡片")
      }
    }

    for card in cards {
      // 应用过滤器
      if let filterCreditCard = filterCreditCard, card.isCredit != filterCreditCard {
        if debug {
          print("\n跳过卡片: \(card.name) (与过滤器不匹配)")
        }
        continue
      }

      if card.isSelected && card.isStatistics {
        activeCardCount += 1
        let currencyRate = getCurrencyRate(
          from: card.currency, to: baseCurrency, currencies: currencies)

        if debug {
          print(
            "\n卡片: \(card.name) (类型: \(card.isCredit ? "信用卡" : "储蓄卡"), 货币: \(card.currency), 汇率: \(currencyRate))"
          )
        }

        if !card.isCredit {
          if card.balance >= 0 {
            // 储蓄卡正余额计入资产
            let cardValue = card.balance * currencyRate
            totalCards += cardValue
            if debug {
              print("  储蓄卡: 余额 \(card.balance) × 汇率 \(currencyRate) = \(cardValue) (资产)")
            }
          } else {
            // 储蓄卡负余额(透支)计入负债
            let debtValue = abs(card.balance) * currencyRate
            totalLiabilities += debtValue
            if debug {
              print("  储蓄卡(透支): 余额 \(card.balance) × 汇率 \(currencyRate) = \(debtValue) (负债)")
            }
          }
        } else if card.isCredit {
          if debug {
            print("  信用卡: 欠款值 \(card.balance)")
          }

          if card.balance < 0 {
            // 负值表示有欠款，计入负债
            let debtValue = abs(card.balance) * currencyRate
            totalLiabilities += debtValue
            if debug {
              print("    欠款(负值): 计入负债 \(debtValue)")
            }
          } else {
            // 正值表示有溢缴，计入资产
            let overpayValue = card.balance * currencyRate
            totalCards += overpayValue
            if debug {
              print("    溢缴(正值): 计入资产 \(overpayValue)")
            }
          }
        }
      } else if debug {
        print("\n跳过卡片: \(card.name) (不计入总资产)")
      }
    }

    let netCards = totalCards - totalLiabilities

    if debug {
      print("\n计算结果:")
      print("  总资产: \(totalCards)")
      print("  总负债: \(totalLiabilities)")
      print("  净资产: \(netCards)")
      print("  活跃卡片数: \(activeCardCount)")
      print("===== 净资产计算结束 =====\n")
    }

    return CardCalculationResult(
      totalCards: totalCards,
      totalLiabilities: totalLiabilities,
      netCards: netCards,
      activeCardCount: activeCardCount
    )
  }

  /// 计算卡片、负债和净资产
  /// - Warning: 此方法是为了兼容性保留，建议使用 calculateCardsDetailed 方法
  /// - Parameters:
  ///   - cards: 卡片列表
  ///   - currencies: 货币列表
  ///   - baseCurrencyCode: 基准货币代码
  ///   - filterCreditCard: 卡片类型过滤器
  /// - Returns: 计算结果元组
  // MARK: - 新增便捷方法

  /// 格式化带符号的金额
  /// - Parameters:
  ///   - amount: 金额数值
  ///   - currencyCode: 货币代码
  ///   - currencies: 可用货币列表
  ///   - showPositiveSign: 是否为正数显示加号
  /// - Returns: 格式化后的金额字符串

  /// 格式化用于输入框的金额
  /// - Parameter amount: 金额数值
  /// - Returns: 格式化后的金额字符串
  func formatAmountForInput(_ amount: Double) -> String {
    return NumberFormatService.shared.formatAmountForInput(amount)
  }

  /// 使用交易记录的历史汇率进行金额转换
  /// - Parameters:
  ///   - amount: 原始金额
  ///   - transaction: 交易记录
  ///   - targetCurrency: 目标货币代码
  ///   - forExpense: 是否为支出类型转换
  /// - Returns: 转换后的金额
  func convertAmountUsingTransactionRate(
    amount: Double,
    transaction: TransactionModel,
    targetCurrency: String,
    forExpense: Bool
  ) -> Double {
    // 如果交易货币与目标货币相同，无需转换
    if transaction.currency == targetCurrency {
      return amount
    }

    // 获取本位币代码
    let baseCurrencyCode = self.baseCurrencyCode

    // 根据目标货币和交易类型选择合适的汇率
    let rate: Double
    if targetCurrency == baseCurrencyCode {
      // 转换到本位币
      if forExpense {
        rate = transaction.expenseToBaseRate
      } else {
        rate = transaction.incomeToBaseRate
      }
    } else {
      // 转换到卡片货币
      if forExpense {
        rate = transaction.expenseToCardRate
      } else {
        rate = transaction.incomeToCardRate
      }
    }

    return amount * rate
  }

  /// 便捷方法：转换交易金额到指定货币
  ///
  /// 根据交易类型自动选择合适的汇率字段，将交易金额转换为目标货币。
  /// 这是一个通用的转换方法，适用于各种统计和分析场景。
  ///
  /// - Parameters:
  ///   - transaction: 交易记录（包含历史汇率）
  ///   - targetCurrency: 目标货币代码
  /// - Returns: 转换后的金额
  func convertTransactionAmountToTargetCurrency(
    transaction: TransactionModel,
    targetCurrency: String
  ) -> Double {
    // 获取实际交易金额（扣除优惠）
    let actualAmount = transaction.transactionAmount - (transaction.discountAmount ?? 0)

    // 根据交易类型确定是否为支出类型
    let forExpense: Bool
    switch transaction.transactionType {
    case .expense:
      forExpense = true
    case .income, .refund:
      forExpense = false
    case .transfer:
      // 转账交易：这里返回支出金额（从转出卡片角度）
      forExpense = true
    case .createCard, .adjustCard:
      // 系统交易：根据金额正负判断
      forExpense = actualAmount < 0
    }

    return convertAmountUsingTransactionRate(
      amount: abs(actualAmount),
      transaction: transaction,
      targetCurrency: targetCurrency,
      forExpense: forExpense
    )
  }

  /// 便捷方法：获取交易的收入和支出金额（转换为目标货币）
  ///
  /// 专门用于统计分析，返回交易在目标货币下的收入和支出金额。
  /// 对于转账交易，会同时返回收入和支出金额。
  ///
  /// - Parameters:
  ///   - transaction: 交易记录
  ///   - targetCurrency: 目标货币代码
  /// - Returns: 元组 (收入金额, 支出金额)
  func getTransactionIncomeExpenseAmounts(
    transaction: TransactionModel,
    targetCurrency: String
  ) -> (income: Double, expense: Double) {
    let actualAmount = transaction.transactionAmount - (transaction.discountAmount ?? 0)
    let absAmount = abs(actualAmount)

    switch transaction.transactionType {
    case .income:
      let convertedAmount = convertAmountUsingTransactionRate(
        amount: absAmount,
        transaction: transaction,
        targetCurrency: targetCurrency,
        forExpense: false
      )
      return (income: convertedAmount, expense: 0)

    case .expense:
      let convertedAmount = convertAmountUsingTransactionRate(
        amount: absAmount,
        transaction: transaction,
        targetCurrency: targetCurrency,
        forExpense: true
      )
      return (income: 0, expense: convertedAmount)

    case .transfer:
      // 转账同时计入收入和支出，分别使用相应汇率
      let expenseConverted = convertAmountUsingTransactionRate(
        amount: absAmount,
        transaction: transaction,
        targetCurrency: targetCurrency,
        forExpense: true
      )
      let incomeConverted = convertAmountUsingTransactionRate(
        amount: absAmount,
        transaction: transaction,
        targetCurrency: targetCurrency,
        forExpense: false
      )
      return (income: incomeConverted, expense: expenseConverted)

    case .refund:
      let convertedAmount = convertAmountUsingTransactionRate(
        amount: transaction.refundAmount ?? absAmount,
        transaction: transaction,
        targetCurrency: targetCurrency,
        forExpense: false
      )
      return (income: convertedAmount, expense: 0)

    case .createCard, .adjustCard:
      // 系统交易不计入收支统计
      return (income: 0, expense: 0)
    }
  }

  /// 检查交易编辑是否需要更新汇率
  ///
  /// 判断交易编辑的变更是否会影响汇率计算，只有在货币相关的变更时才需要更新汇率。
  /// 这避免了不必要的汇率覆盖，保护历史汇率数据。
  ///
  /// - Parameters:
  ///   - originalTransaction: 原始交易
  ///   - newCurrency: 新的交易货币（如果有变更）
  ///   - newFromCardId: 新的支出卡片ID（如果有变更）
  ///   - newToCardId: 新的收入卡片ID（如果有变更）
  ///   - cards: 卡片列表
  /// - Returns: 是否需要更新汇率
  func shouldUpdateExchangeRates(
    originalTransaction: TransactionModel,
    newCurrency: String? = nil,
    newFromCardId: UUID? = nil,
    newToCardId: UUID? = nil,
    cards: [CardModel]
  ) -> Bool {
    // 如果交易货币发生变化，需要更新汇率
    if let newCurrency = newCurrency, newCurrency != originalTransaction.currency {
      return true
    }

    // 如果卡片发生变化且涉及不同货币，需要更新汇率
    if let newFromCardId = newFromCardId, newFromCardId != originalTransaction.fromCardId {
      let originalCard = cards.first { $0.id == originalTransaction.fromCardId }
      let newCard = cards.first { $0.id == newFromCardId }
      if originalCard?.currency != newCard?.currency {
        return true
      }
    }

    if let newToCardId = newToCardId, newToCardId != originalTransaction.toCardId {
      let originalCard = cards.first { $0.id == originalTransaction.toCardId }
      let newCard = cards.first { $0.id == newToCardId }
      if originalCard?.currency != newCard?.currency {
        return true
      }
    }

    return false
  }

  /// 安全地更新交易汇率
  ///
  /// 只在必要时更新交易的汇率信息，保护历史汇率数据不被意外覆盖。
  /// 使用当前汇率重新计算，但只在货币相关变更时执行。
  ///
  /// - Parameters:
  ///   - transaction: 要更新的交易
  ///   - cards: 卡片列表
  ///   - currencies: 货币列表
  func updateTransactionExchangeRatesIfNeeded(
    transaction: TransactionModel,
    cards: [CardModel],
    currencies: [CurrencyModel]
  ) {
    let baseCurrencyCode = currencies.first(where: { $0.isBaseCurrency })?.code ?? "CNY"

    switch transaction.transactionType {
    case .expense:
      updateExpenseTransactionRates(
        transaction: transaction, cards: cards, currencies: currencies,
        baseCurrencyCode: baseCurrencyCode)
    case .income:
      updateIncomeTransactionRates(
        transaction: transaction, cards: cards, currencies: currencies,
        baseCurrencyCode: baseCurrencyCode)
    case .transfer:
      updateTransferTransactionRates(
        transaction: transaction, cards: cards, currencies: currencies,
        baseCurrencyCode: baseCurrencyCode)
    case .refund:
      updateRefundTransactionRates(
        transaction: transaction, cards: cards, currencies: currencies,
        baseCurrencyCode: baseCurrencyCode)
    default:
      break
    }
  }

  // MARK: - 私有汇率更新方法

  /// 更新支出交易的汇率
  private func updateExpenseTransactionRates(
    transaction: TransactionModel,
    cards: [CardModel],
    currencies: [CurrencyModel],
    baseCurrencyCode: String
  ) {
    guard let card = cards.first(where: { $0.id == transaction.fromCardId }) else { return }

    let transactionCurrency = transaction.currency.isEmpty ? baseCurrencyCode : transaction.currency
    let cardCurrency = card.currency

    // 计算交易货币到卡片货币的汇率
    transaction.expenseToCardRate = getCurrencyRate(
      from: transactionCurrency, to: cardCurrency, currencies: currencies)

    // 计算交易货币到本位币的汇率
    transaction.expenseToBaseRate = getCurrencyRate(
      from: transactionCurrency, to: baseCurrencyCode, currencies: currencies)

    // 收入相关汇率保持为1.0（支出交易不涉及收入）
    transaction.incomeToCardRate = 1.0
    transaction.incomeToBaseRate = 1.0

    print(
      "💱 支出交易汇率更新完成: \(transactionCurrency) -> \(cardCurrency)(\(transaction.expenseToCardRate)), \(baseCurrencyCode)(\(transaction.expenseToBaseRate))"
    )
  }

  /// 更新收入交易的汇率
  private func updateIncomeTransactionRates(
    transaction: TransactionModel,
    cards: [CardModel],
    currencies: [CurrencyModel],
    baseCurrencyCode: String
  ) {
    guard let card = cards.first(where: { $0.id == transaction.toCardId }) else { return }

    let transactionCurrency = transaction.currency.isEmpty ? baseCurrencyCode : transaction.currency
    let cardCurrency = card.currency

    // 支出相关汇率保持为1.0（收入交易不涉及支出）
    transaction.expenseToCardRate = 1.0
    transaction.expenseToBaseRate = 1.0

    // 计算交易货币到卡片货币的汇率
    transaction.incomeToCardRate = getCurrencyRate(
      from: transactionCurrency, to: cardCurrency, currencies: currencies)

    // 计算交易货币到本位币的汇率
    transaction.incomeToBaseRate = getCurrencyRate(
      from: transactionCurrency, to: baseCurrencyCode, currencies: currencies)

    print(
      "💱 收入交易汇率更新完成: \(transactionCurrency) -> \(cardCurrency)(\(transaction.incomeToCardRate)), \(baseCurrencyCode)(\(transaction.incomeToBaseRate))"
    )
  }

  /// 更新转账交易的汇率
  private func updateTransferTransactionRates(
    transaction: TransactionModel,
    cards: [CardModel],
    currencies: [CurrencyModel],
    baseCurrencyCode: String
  ) {
    guard let fromCard = cards.first(where: { $0.id == transaction.fromCardId }),
      let toCard = cards.first(where: { $0.id == transaction.toCardId })
    else { return }

    let transactionCurrency = transaction.currency.isEmpty ? baseCurrencyCode : transaction.currency
    let fromCardCurrency = fromCard.currency
    let toCardCurrency = toCard.currency

    // 计算交易货币到转出卡片货币的汇率
    transaction.expenseToCardRate = getCurrencyRate(
      from: transactionCurrency, to: fromCardCurrency, currencies: currencies)

    // 计算交易货币到转入卡片货币的汇率
    transaction.incomeToCardRate = getCurrencyRate(
      from: transactionCurrency, to: toCardCurrency, currencies: currencies)

    // 计算交易货币到本位币的汇率
    let toBaseRate = getCurrencyRate(
      from: transactionCurrency, to: baseCurrencyCode, currencies: currencies)
    transaction.expenseToBaseRate = toBaseRate
    transaction.incomeToBaseRate = toBaseRate

    print(
      "💱 转账交易汇率更新完成: \(transactionCurrency) -> 转出(\(transaction.expenseToCardRate)), 转入(\(transaction.incomeToCardRate)), 本位币(\(toBaseRate))"
    )
  }

  /// 更新退款交易的汇率
  private func updateRefundTransactionRates(
    transaction: TransactionModel,
    cards: [CardModel],
    currencies: [CurrencyModel],
    baseCurrencyCode: String
  ) {
    guard let card = cards.first(where: { $0.id == transaction.toCardId }) else { return }

    let transactionCurrency = transaction.currency.isEmpty ? baseCurrencyCode : transaction.currency
    let cardCurrency = card.currency

    // 支出相关汇率保持为1.0（退款交易不涉及支出）
    transaction.expenseToCardRate = 1.0
    transaction.expenseToBaseRate = 1.0

    // 计算交易货币到卡片货币的汇率
    transaction.incomeToCardRate = getCurrencyRate(
      from: transactionCurrency, to: cardCurrency, currencies: currencies)

    // 计算交易货币到本位币的汇率
    transaction.incomeToBaseRate = getCurrencyRate(
      from: transactionCurrency, to: baseCurrencyCode, currencies: currencies)

    print(
      "💱 退款交易汇率更新完成: \(transactionCurrency) -> \(cardCurrency)(\(transaction.incomeToCardRate)), \(baseCurrencyCode)(\(transaction.incomeToBaseRate))"
    )
  }

}

// MARK: - Supporting Types

/// 货币映射数据结构
///
/// 从 JSON 文件加载的货币配置信息。
struct CurrencyMappingsResponse: Codable {
  /// 货币代码到名称的映射
  let currencyNames: [String: String]
  /// 货币代码到符号的映射
  let currencySymbols: [String: String]
  /// 常用货币代码列表
  let priorityCurrencies: [String]
}
