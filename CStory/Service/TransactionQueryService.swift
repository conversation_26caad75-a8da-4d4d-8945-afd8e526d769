import Foundation
import SwiftData

/// 交易查询服务
///
/// 提供交易数据查询、筛选、分组功能，消除跨组件的重复查询代码。
final class TransactionQueryService {

  /// 单例实例
  static let shared = TransactionQueryService()

  /// 私有初始化器，确保单例模式
  private init() {}

  // MARK: - Basic Query Operations

  /// 根据日期范围查询交易
  /// - Parameters:
  ///   - startDate: 开始日期
  ///   - endDate: 结束日期
  ///   - context: SwiftData模型上下文
  /// - Returns: 符合条件的交易列表
  func getTransactions(
    from startDate: Date,
    to endDate: Date,
    context: ModelContext
  ) -> [TransactionModel] {
    let descriptor = FetchDescriptor<TransactionModel>(
      predicate: #Predicate<TransactionModel> { transaction in
        transaction.transactionDate >= startDate && transaction.transactionDate <= endDate
      },
      sortBy: [SortDescriptor(\.transactionDate, order: .reverse)]
    )

    do {
      return try context.fetch(descriptor)
    } catch {
      print("❌ 查询交易失败: \(error)")
      return []
    }
  }

  /// 根据卡片ID查询交易
  ///
  /// 查询与指定卡片相关的所有交易记录（包括转出和转入）。
  ///
  /// - Parameters:
  ///   - cardId: 卡片ID
  ///   - context: SwiftData模型上下文
  ///   - limit: 查询数量限制，nil表示不限制
  /// - Returns: 相关交易列表
  func getTransactions(
    for cardId: UUID,
    context: ModelContext,
    limit: Int? = nil
  ) -> [TransactionModel] {
    var descriptor = FetchDescriptor<TransactionModel>(
      predicate: #Predicate<TransactionModel> { transaction in
        transaction.fromCardId == cardId || transaction.toCardId == cardId
      },
      sortBy: [SortDescriptor(\.transactionDate, order: .reverse)]
    )

    if let limit = limit {
      descriptor.fetchLimit = limit
    }

    do {
      return try context.fetch(descriptor)
    } catch {
      print("❌ 查询卡片交易失败: \(error)")
      return []
    }
  }

  /// 获取最近N天的交易
  ///
  /// 查询最近指定天数内的交易记录。
  ///
  /// - Parameters:
  ///   - days: 天数
  ///   - context: SwiftData模型上下文
  ///   - excludeTypes: 要排除的交易类型
  /// - Returns: 最近交易列表
  func getRecentTransactions(
    days: Int,
    context: ModelContext,
    excludeTypes: [TransactionType] = []
  ) -> [TransactionModel] {
    let (startDate, endDate) = DateHelperService.shared.getRecentDaysRange(days: days)

    let descriptor = FetchDescriptor<TransactionModel>(
      predicate: #Predicate<TransactionModel> { transaction in
        transaction.transactionDate >= startDate && transaction.transactionDate <= endDate
      },
      sortBy: [SortDescriptor(\.transactionDate, order: .reverse)]
    )

    do {
      let allTransactions: [TransactionModel] = try context.fetch(descriptor)

      if excludeTypes.isEmpty {
        return allTransactions
      } else {
        return allTransactions.filter { transaction in
          !excludeTypes.contains(transaction.transactionType)
        }
      }
    } catch {
      print("❌ 查询最近交易失败: \(error)")
      return []
    }
  }

  // MARK: - Filtering Operations

  /// 按交易类型筛选交易
  ///
  /// 从给定的交易列表中筛选出指定类型的交易。
  ///
  /// - Parameters:
  ///   - transactions: 原始交易列表
  ///   - types: 要筛选的交易类型
  /// - Returns: 筛选后的交易列表
  func filterTransactions(
    _ transactions: [TransactionModel],
    byTypes types: [TransactionType]
  ) -> [TransactionModel] {
    return transactions.filter { transaction in
      types.contains(transaction.transactionType)
    }
  }

  /// 按分类筛选交易
  ///
  /// 从给定的交易列表中筛选出指定分类的交易。
  ///
  /// - Parameters:
  ///   - transactions: 原始交易列表
  ///   - categoryIds: 分类ID列表（支持主分类和子分类）
  /// - Returns: 筛选后的交易列表
  func filterTransactions(
    _ transactions: [TransactionModel],
    byCategories categoryIds: [String]
  ) -> [TransactionModel] {
    return transactions.filter { transaction in
      guard let categoryId = transaction.transactionCategoryId else {
        return false
      }
      return categoryIds.contains(categoryId)
    }
  }

  /// 按金额范围筛选交易
  ///
  /// 从给定的交易列表中筛选出指定金额范围的交易。
  ///
  /// - Parameters:
  ///   - transactions: 原始交易列表
  ///   - minAmount: 最小金额（可选）
  ///   - maxAmount: 最大金额（可选）
  /// - Returns: 筛选后的交易列表
  func filterTransactions(
    _ transactions: [TransactionModel],
    minAmount: Double? = nil,
    maxAmount: Double? = nil
  ) -> [TransactionModel] {
    return transactions.filter { transaction in
      let amount = transaction.transactionAmount

      if let min = minAmount, amount < min {
        return false
      }

      if let max = maxAmount, amount > max {
        return false
      }

      return true
    }
  }

  /// 按关键词搜索交易
  ///
  /// 在交易的备注、商家名称等字段中搜索关键词。
  ///
  /// - Parameters:
  ///   - transactions: 原始交易列表
  ///   - keyword: 搜索关键词
  /// - Returns: 包含关键词的交易列表
  func searchTransactions(
    _ transactions: [TransactionModel],
    keyword: String
  ) -> [TransactionModel] {
    let trimmedKeyword = keyword.trimmingCharacters(in: .whitespacesAndNewlines)

    guard !trimmedKeyword.isEmpty else {
      return transactions
    }

    return transactions.filter { transaction in
      // 搜索备注
      if !transaction.remark.isEmpty
        && transaction.remark.localizedCaseInsensitiveContains(trimmedKeyword)
      {
        return true
      }

      return false
    }
  }

  // MARK: - Grouping Operations

  /// 按日期分组交易
  ///
  /// 将交易列表按日期进行分组，返回按日期排序的分组结果。
  ///
  /// - Parameter transactions: 交易列表
  /// - Returns: 按日期分组的交易字典
  func groupTransactionsByDate(
    _ transactions: [TransactionModel]
  ) -> [Date: [TransactionModel]] {
    let calendar = Calendar.current

    let grouped = Dictionary(grouping: transactions) { transaction in
      calendar.startOfDay(for: transaction.transactionDate)
    }

    return grouped
  }

  /// 按月份分组交易
  ///
  /// 将交易列表按月份进行分组。
  ///
  /// - Parameter transactions: 交易列表
  /// - Returns: 按月份分组的交易字典
  func groupTransactionsByMonth(
    _ transactions: [TransactionModel]
  ) -> [Date: [TransactionModel]] {
    let calendar = Calendar.current

    let grouped = Dictionary(grouping: transactions) { transaction in
      let components = calendar.dateComponents([.year, .month], from: transaction.transactionDate)
      return calendar.date(from: components) ?? transaction.transactionDate
    }

    return grouped
  }

  /// 按交易类型分组交易
  ///
  /// 将交易列表按交易类型进行分组。
  ///
  /// - Parameter transactions: 交易列表
  /// - Returns: 按交易类型分组的交易字典
  func groupTransactionsByType(
    _ transactions: [TransactionModel]
  ) -> [TransactionType: [TransactionModel]] {
    return Dictionary(grouping: transactions) { transaction in
      transaction.transactionType
    }
  }

  /// 按分类分组交易
  ///
  /// 将交易列表按分类进行分组。
  ///
  /// - Parameter transactions: 交易列表
  /// - Returns: 按分类分组的交易字典
  func groupTransactionsByCategory(
    _ transactions: [TransactionModel]
  ) -> [String: [TransactionModel]] {
    return Dictionary(grouping: transactions) { transaction in
      transaction.transactionCategoryId ?? "未分类"
    }
  }

  // MARK: - Statistical Operations

  /// 计算交易列表的总金额
  ///
  /// 根据交易类型计算总收入、总支出或净金额。
  ///
  /// - Parameters:
  ///   - transactions: 交易列表
  ///   - calculation: 计算类型
  /// - Returns: 计算结果
  func calculateTotal(
    for transactions: [TransactionModel],
    calculation: TotalCalculationType
  ) -> Double {
    switch calculation {
    case .income:
      return
        transactions
        .filter { $0.transactionType == .income || $0.transactionType == .refund }
        .reduce(0) { $0 + $1.transactionAmount }

    case .expense:
      return
        transactions
        .filter { $0.transactionType == .expense }
        .reduce(0) { $0 + ($1.transactionAmount - ($1.discountAmount ?? 0)) }

    case .net:
      let income = calculateTotal(for: transactions, calculation: .income)
      let expense = calculateTotal(for: transactions, calculation: .expense)
      return income - expense

    case .transfer:
      return
        transactions
        .filter { $0.transactionType == .transfer }
        .reduce(0) { $0 + $1.transactionAmount }
    }
  }

  /// 获取交易统计摘要
  ///
  /// 返回交易列表的详细统计信息。
  ///
  /// - Parameter transactions: 交易列表
  /// - Returns: 统计摘要
  func getTransactionSummary(
    for transactions: [TransactionModel]
  ) -> TransactionSummary {
    let typeGroups = groupTransactionsByType(transactions)

    let incomeTransactions = typeGroups[.income] ?? []
    let expenseTransactions = typeGroups[.expense] ?? []
    let transferTransactions = typeGroups[.transfer] ?? []
    let refundTransactions = typeGroups[.refund] ?? []

    let totalIncome = incomeTransactions.reduce(0) { $0 + $1.transactionAmount }
    let totalExpense = expenseTransactions.reduce(0) {
      $0 + ($1.transactionAmount - ($1.discountAmount ?? 0))
    }
    let totalTransfer = transferTransactions.reduce(0) { $0 + $1.transactionAmount }
    let totalRefund = refundTransactions.reduce(0) { $0 + $1.transactionAmount }

    return TransactionSummary(
      totalCount: transactions.count,
      incomeCount: incomeTransactions.count,
      expenseCount: expenseTransactions.count,
      transferCount: transferTransactions.count,
      refundCount: refundTransactions.count,
      totalIncome: totalIncome,
      totalExpense: totalExpense,
      totalTransfer: totalTransfer,
      totalRefund: totalRefund,
      netAmount: totalIncome + totalRefund - totalExpense
    )
  }

  // MARK: - Advanced Query Operations

  /// 查询指定时间段内的分类统计
  ///
  /// 查询指定时间段内各分类的交易统计信息。
  ///
  /// - Parameters:
  ///   - startDate: 开始日期
  ///   - endDate: 结束日期
  ///   - transactionType: 交易类型筛选
  ///   - context: SwiftData模型上下文
  /// - Returns: 分类统计列表
  func getCategoryStatistics(
    from startDate: Date,
    to endDate: Date,
    transactionType: TransactionType? = nil,
    context: ModelContext
  ) -> [CategoryStatistic] {
    let transactions = getTransactions(from: startDate, to: endDate, context: context)

    let filteredTransactions =
      transactionType != nil
      ? filterTransactions(transactions, byTypes: [transactionType!])
      : transactions

    let categoryGroups = groupTransactionsByCategory(filteredTransactions)

    return categoryGroups.map { (categoryId, transactions) in
      let totalAmount = transactions.reduce(0) { sum, transaction in
        let amount = transaction.transactionAmount - (transaction.discountAmount ?? 0)
        return sum + amount
      }

      return CategoryStatistic(
        categoryId: categoryId,
        transactionCount: transactions.count,
        totalAmount: totalAmount,
        transactions: transactions
      )
    }.sorted { $0.totalAmount > $1.totalAmount }
  }

  /// 获取月度趋势数据
  ///
  /// 获取指定时间范围内的月度交易趋势数据。
  ///
  /// - Parameters:
  ///   - startDate: 开始日期
  ///   - endDate: 结束日期
  ///   - context: SwiftData模型上下文
  /// - Returns: 月度趋势数据
  func getMonthlyTrend(
    from startDate: Date,
    to endDate: Date,
    context: ModelContext
  ) -> [MonthlyTrendData] {
    let transactions = getTransactions(from: startDate, to: endDate, context: context)
    let monthlyGroups = groupTransactionsByMonth(transactions)

    return monthlyGroups.map { (month, transactions) in
      let summary = getTransactionSummary(for: transactions)

      return MonthlyTrendData(
        month: month,
        income: summary.totalIncome,
        expense: summary.totalExpense,
        net: summary.netAmount,
        transactionCount: summary.totalCount
      )
    }.sorted { $0.month < $1.month }
  }
}

// MARK: - Supporting Types

/// 总额计算类型
enum TotalCalculationType {
  case income  // 总收入
  case expense  // 总支出
  case net  // 净额（收入-支出）
  case transfer  // 转账总额
}

/// 交易统计摘要
struct TransactionSummary {
  let totalCount: Int
  let incomeCount: Int
  let expenseCount: Int
  let transferCount: Int
  let refundCount: Int
  let totalIncome: Double
  let totalExpense: Double
  let totalTransfer: Double
  let totalRefund: Double
  let netAmount: Double
}

/// 分类统计信息
struct CategoryStatistic {
  let categoryId: String
  let transactionCount: Int
  let totalAmount: Double
  let transactions: [TransactionModel]
}

/// 月度趋势数据
struct MonthlyTrendData {
  let month: Date
  let income: Double
  let expense: Double
  let net: Double
  let transactionCount: Int
}
